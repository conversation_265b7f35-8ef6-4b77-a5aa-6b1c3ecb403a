/**
 * Enhanced Chart Store with improved type safety, error handling, and state management
 * 
 * This is the enhanced version of the chart store with:
 * - Better type safety with structured interfaces
 * - Enhanced error handling with retry logic
 * - Computed selectors for derived state
 * - Performance monitoring
 * - Request validation
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import axios from 'axios';
import type { ChartStore, ChartError, LoadingState, StoreConfig, ChartTabName } from './types';
import { 
  errors, 
  isRetryableError, 
  validateChartRequest, 
  isSameRequest, 
  calculateRetryDelay,
  isValidChartState,
  getNextLoadingState,
  createPerformanceLogger
} from './utils';
import { selectLoadingCompat } from './selectors';
import { storeLogger } from '../../../utils/logger';
import { fetchChartData } from '../services/api/chartApi';
import { STORE_CONFIG } from '../config';

// Store Configuration using centralized config
const config: StoreConfig = {
  maxRetries: STORE_CONFIG.retry.maxRetries,
  retryDelay: STORE_CONFIG.retry.baseDelay,
  enablePersistence: STORE_CONFIG.persistence.enabled,
  persistenceKey: STORE_CONFIG.persistence.key,
};

/**
 * Enhanced Zustand store for managing chart data state
 * 
 * Features:
 * - Structured error handling with retry logic
 * - Performance monitoring
 * - Request validation
 * - Granular loading states
 * - Computed selectors
 */
const useChartStoreEnhanced = create<ChartStore>()(
  subscribeWithSelector((set, get) => ({
    // State
    chartInfo: null,
    loadingState: 'idle' as LoadingState,
    error: null,
    lastRequest: null,
    lastUpdated: null,
    retryCount: 0,
    dirtyTabs: {
      ed: false,
      obs: false,
      profee: false,
    },

    // Computed property - will be added as a regular field
    isLoading: false,

    // Actions
    loadChartData: async (facilityId: number, visitId: string) => {
      const perf = createPerformanceLogger();
      perf.start();

      try {
        // Validate request
        const validationErrors = validateChartRequest(facilityId, visitId);
        if (validationErrors.length > 0) {
          const error = errors.validationError(
            `Invalid request: ${validationErrors.join(', ')}`,
            { facilityId, visitId }
          );
          set({ 
            error,
            loadingState: getNextLoadingState('idle', 'error'),
            isLoading: false
          });
          return;
        }

        // Check if this is the same request as last time
        const currentState = get();
        if (
          currentState.loadingState === 'loading' ||
          (isSameRequest(currentState.lastRequest, { facilityId, visitId }) && 
           currentState.loadingState === 'success' && 
           currentState.chartInfo)
        ) {
          storeLogger.debug('Skipping duplicate or in-progress request', { facilityId, visitId });
          return;
        }

        // Set loading state
        set({ 
          loadingState: getNextLoadingState('idle', 'start'),
          isLoading: true,
          error: null,
          lastRequest: { facilityId, visitId, timestamp: new Date() }
        });

        storeLogger.debug('Loading chart data', { facilityId, visitId });

        // Fetch data
        const chartData = await fetchChartData(facilityId, visitId);
        
        // Validate response
        if (!isValidChartState(chartData)) {
          throw new Error('Invalid chart data received from API');
        }

        // Success
        set({ 
          chartInfo: chartData,
          loadingState: getNextLoadingState('loading', 'success'),
          isLoading: false,
          lastUpdated: new Date(),
          retryCount: 0,
          error: null
        });

        perf.end('loadChartData');
        storeLogger.debug('Successfully loaded chart data', { 
          facilityId, 
          visitId,
          duration: perf.end('loadChartData')
        });

      } catch (error) {
        perf.end('loadChartData (error)');
        
        let chartError: ChartError;
        const context = { facilityId, visitId, retryCount: get().retryCount };

        if (axios.isAxiosError(error)) {
          storeLogger.error('Axios error loading chart data', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            facilityId,
            visitId
          });

          // Map HTTP status codes to specific errors
          switch (error.response?.status) {
            case 401:
            case 403:
              chartError = errors.authError(context);
              break;
            case 404:
              chartError = errors.notFoundError(context);
              break;
            case 408:
              chartError = errors.timeoutError(context);
              break;
            case 500:
            case 502:
            case 503:
              chartError = errors.serverError(context);
              break;
            default:
              chartError = errors.networkError(context);
          }
        } else {
          storeLogger.error('General error loading chart data', { error, facilityId, visitId });
          chartError = errors.networkError(context);
        }

        set({ 
          error: chartError,
          loadingState: getNextLoadingState('loading', 'error'),
          isLoading: false,
          chartInfo: null
        });
      }
    },

    retryLastRequest: async () => {
      const state = get();
      
      if (!state.lastRequest || !state.error) {
        storeLogger.warn('Cannot retry: no previous request or error');
        return;
      }

      if (!isRetryableError(state.error)) {
        storeLogger.warn('Cannot retry: error is not retryable', { errorCode: state.error.code });
        return;
      }

      if (state.retryCount >= config.maxRetries) {
        storeLogger.warn('Cannot retry: max retries exceeded', { retryCount: state.retryCount });
        return;
      }

      // Increment retry count
      set({ retryCount: state.retryCount + 1 });

      // Calculate delay
      const delay = calculateRetryDelay(state.retryCount, config.retryDelay);
      storeLogger.debug('Retrying request', { 
        delay, 
        retryCount: state.retryCount + 1,
        facilityId: state.lastRequest.facilityId,
        visitId: state.lastRequest.visitId
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));

      // Retry the request
      if (state.lastRequest.facilityId && state.lastRequest.visitId) {
        await get().loadChartData(state.lastRequest.facilityId, state.lastRequest.visitId);
      }
    },

    clearChartData: () => {
      storeLogger.debug('Clearing chart data');
      set({ 
        chartInfo: null, 
        loadingState: getNextLoadingState('success', 'reset'),
        isLoading: false,
        error: null,
        lastUpdated: null
      });
    },

    clearError: () => {
      set({ error: null });
      if (get().loadingState === 'error') {
        set({ 
          loadingState: getNextLoadingState('error', 'reset'),
          isLoading: false
        });
      }
    },

    resetStore: () => {
      storeLogger.debug('Resetting store to initial state');
      set({
        chartInfo: null,
        loadingState: 'idle',
        isLoading: false,
        error: null,
        lastRequest: null,
        lastUpdated: null,
        retryCount: 0,
      });
    },

    setError: (error: ChartError) => {
      set({ 
        error,
        loadingState: getNextLoadingState(get().loadingState, 'error'),
        isLoading: false
      });
    },

    incrementRetry: () => {
      set({ retryCount: get().retryCount + 1 });
    },

    // Dirty tracking actions
    markTabDirty: (tab: ChartTabName) => {
      set((state) => ({
        dirtyTabs: {
          ...state.dirtyTabs,
          [tab]: true,
        },
      }));
      storeLogger.debug(`Marked ${tab} tab as dirty`);
    },

    clearTabDirty: (tab: ChartTabName) => {
      set((state) => ({
        dirtyTabs: {
          ...state.dirtyTabs,
          [tab]: false,
        },
      }));
      storeLogger.debug(`Cleared dirty state for ${tab} tab`);
    },

    clearAllDirty: () => {
      set({
        dirtyTabs: {
          ed: false,
          obs: false,
          profee: false,
        },
      });
      storeLogger.debug('Cleared all dirty states');
    },

    hasAnyDirtyTab: () => {
      const { dirtyTabs } = get();
      return dirtyTabs.ed || dirtyTabs.obs || dirtyTabs.profee;
    },
  }))
);

export default useChartStoreEnhanced;