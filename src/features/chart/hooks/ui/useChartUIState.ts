import { useState } from 'react';

/**
 * Hook to manage chart page UI state
 * Extracts tab, accordion, and modal state management from ChartPage
 */
export const useChartUIState = () => {
  // Tab state management
  const [selectedTab, setSelectedTab] = useState(0);

  // E&M Level inline state
  const [isEAndMLevelInlineOpen, setIsEAndMLevelInlineOpen] = useState(false);

  // Handle tab changes
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  // Toggle E&M level inline display
  const handleToggleEAndMLevelInline = () => {
    setIsEAndMLevelInlineOpen(prev => !prev);
  };

  return {
    // Tab state
    selectedTab,
    setSelectedTab,
    handleTabChange,
    
    // E&M Level state
    isEAndMLevelInlineOpen,
    setIsEAndMLevelInlineOpen,
    handleToggleEAndMLevelInline,
  };
};

/**
 * Hook specifically for E&M Level inline state management
 * Can be used in components that only need E&M state
 */
export const useEMInlineState = (defaultOpen: boolean = false) => {
  const [isEAndMLevelInlineOpen, setIsEAndMLevelInlineOpen] = useState(defaultOpen);

  const handleToggleEAndMLevelInline = () => {
    setIsEAndMLevelInlineOpen(prev => !prev);
  };

  return {
    isEAndMLevelInlineOpen,
    setIsEAndMLevelInlineOpen,
    handleToggleEAndMLevelInline,
  };
}; 