/**
 * Hook for tracking dirty state across chart tabs
 * 
 * This hook integrates React Hook Form's dirty field tracking with
 * the Zustand store to maintain tab-level dirty states. It watches
 * for changes in form fields and automatically updates the dirty
 * state of the corresponding tab.
 */

import { useEffect, useRef } from 'react';
import { useFormState } from 'react-hook-form';
import useChartStoreEnhanced from '../stores/chartStoreEnhanced';
import { getTabForField } from '../config/fieldTabMapping';
import { chartLogger } from '../../../utils/logger';
import type { ChartTabName } from '../stores/types';

/**
 * Custom hook that tracks form dirty state and updates tab dirty indicators
 * 
 * @returns Object containing dirty state information and helper methods
 */
export const useChartDirtyTracking = () => {
  const { dirtyFields, isDirty } = useFormState();
  
  // Use selectors to avoid unnecessary re-renders
  const dirtyTabs = useChartStoreEnhanced(state => state.dirtyTabs);
  const actions = useChartStoreEnhanced(state => ({
    markTabDirty: state.markTabDirty,
    clearTabDirty: state.clearTabDirty,
    clearAllDirty: state.clearAllDirty,
    hasAnyDirtyTab: state.hasAnyDirtyTab,
  }));
  
  // Track previous state to detect actual changes
  const prevStateRef = useRef<{ isDirty: boolean; dirtyFieldsStr: string }>({
    isDirty: false,
    dirtyFieldsStr: '',
  });

  // Track which tabs have dirty fields
  useEffect(() => {
    // Create a stable string representation of dirty fields for comparison
    const dirtyFieldsStr = Object.keys(dirtyFields).sort().join(',');
    const prevState = prevStateRef.current;
    
    // Check if state has actually changed
    if (prevState.isDirty === isDirty && prevState.dirtyFieldsStr === dirtyFieldsStr) {
      return; // No actual changes, skip update
    }
    
    // Update previous state
    prevStateRef.current = { isDirty, dirtyFieldsStr };
    
    if (!isDirty) {
      // If form is not dirty, clear all tab dirty states
      actions.clearAllDirty();
      return;
    }

    // Track which tabs currently have dirty fields
    const tabsWithDirtyFields = new Set<ChartTabName>();
    
    // Process all dirty fields
    Object.keys(dirtyFields).forEach(fieldPath => {
      const tab = getTabForField(fieldPath);
      if (tab) {
        tabsWithDirtyFields.add(tab);
      }
    });

    // Batch update dirty states
    const allTabs: ChartTabName[] = ['ed', 'obs', 'profee'];
    const currentDirtyTabs = useChartStoreEnhanced.getState().dirtyTabs;
    
    allTabs.forEach(tab => {
      const shouldBeDirty = tabsWithDirtyFields.has(tab);
      const isCurrentlyDirty = currentDirtyTabs[tab];
      
      if (shouldBeDirty !== isCurrentlyDirty) {
        if (shouldBeDirty) {
          actions.markTabDirty(tab);
        } else {
          actions.clearTabDirty(tab);
        }
      }
    });

    // Log current dirty state for debugging
    chartLogger.debug('Dirty tracking update', {
      isDirty,
      dirtyFields: Object.keys(dirtyFields),
      dirtyTabs: Array.from(tabsWithDirtyFields),
    });
  }, [isDirty, dirtyFields, actions]);

  return {
    // State
    dirtyTabs,
    isDirty,
    hasAnyDirtyTab: actions.hasAnyDirtyTab(),
    
    // Actions
    clearAllDirty: actions.clearAllDirty,
    
    // Helper to check if a specific tab is dirty
    isTabDirty: (tab: ChartTabName) => dirtyTabs[tab],
    
    // Get count of dirty tabs
    dirtyTabCount: Object.values(dirtyTabs).filter(Boolean).length,
  };
};

/**
 * Hook to get just the dirty state for a specific tab
 * Useful for tab components that only need their own dirty state
 * 
 * @param tab - The tab name to check
 * @returns Whether the tab is dirty
 */
export const useTabDirtyState = (tab: ChartTabName) => {
  const { dirtyTabs } = useChartStoreEnhanced();
  return dirtyTabs[tab];
};