/**
 * Chart Configuration Index
 * Centralized exports for all chart feature configurations
 */

// API and environment configuration
export * from './api';
export * from './cache';

// Store and state management configuration  
export * from './store';

// UI and theme configuration
export * from './ui';

// Business logic and validation configuration
export * from './business';

// Field mapping configuration
export * from './fieldTabMapping';

// Test configuration (only exported in test environment)
export * from './test';

/**
 * Configuration namespaces for organized imports
 */
export { API_CONFIG, ENV_CONFIG, getEnvConfig } from './api';
export { CACHE_CONFIG, CACHE_KEYS, getCacheConfig } from './cache';
export { STORE_CONFIG, VALIDATION_CONFIG, DEFAULT_STATE } from './store';
export { 
  UI_COLORS, 
  UI_SPACING, 
  UI_SIZING, 
  UI_TYPOGRAPHY, 
  UI_ANIMATION, 
  UI_FORMS,
  UI_BREAKPOINTS,
  UI_A11Y
} from './ui';
export { 
  EM_LEVELS_CONFIG, 
  BIL<PERSON>ING_CONFIG, 
  WORKFLOW_CONFIG, 
  VALIDATION_RULES,
  BUSINESS_ERRORS
} from './business';
export { 
  TEST_TIMEOUTS, 
  TEST_OVERRIDES, 
  MOCK_CONFIG, 
  TEST_UTILS,
  JEST_CONFIG
} from './test';

/**
 * Type helpers for configuration objects
 * Note: Consolidated CHART_CONFIG object removed to avoid circular imports
 * Import individual configs directly: import { API_CONFIG, CACHE_CONFIG } from '../config'
 */
export type ApiConfig = typeof API_CONFIG;
export type CacheConfig = typeof CACHE_CONFIG;
export type StoreConfig = typeof STORE_CONFIG;