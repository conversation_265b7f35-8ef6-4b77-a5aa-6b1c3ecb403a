/**
 * Field to Tab Mapping Configuration
 * 
 * Maps form field names to their respective tabs for dirty tracking.
 * This configuration is used to determine which tab should be marked
 * as dirty when a specific field changes.
 */

import type { ChartTabName } from '../stores/types';

/**
 * Mapping of form field names to their respective tabs
 */
export const FIELD_TO_TAB_MAP: Record<string, ChartTabName> = {
  // ED Tab fields
  treatmentArea: 'ed',
  edChartStatus: 'ed',
  note: 'ed',
  levelCheckboxStates: 'ed',
  // Any nested fields under levelCheckboxStates will be handled by the hook
  
  // Obs Tab fields
  obsStart: 'obs',
  obsEnd: 'obs',
  // TODO: Add additional Obs fields as they are implemented
  // observationDetails: 'obs',
  // observationNotes: 'obs',
  
  // Profee Tab fields
  provider: 'profee',
  criticalCareMins: 'profee',
  traumaActivation: 'profee',
  specialNoCharge: 'profee',
  mod25: 'profee',
  mod59: 'profee',
  // TODO: Add billing codes and insurance fields as they are implemented
  // billingCodes: 'profee',
  // insurance: 'profee',
};

/**
 * Helper function to get the tab for a given field path
 * Handles nested fields (e.g., 'levelCheckboxStates.someId')
 * 
 * @param fieldPath - The field path, potentially nested with dots
 * @returns The tab name or undefined if not mapped
 */
export const getTabForField = (fieldPath: string): ChartTabName | undefined => {
  // First check exact match
  if (FIELD_TO_TAB_MAP[fieldPath]) {
    return FIELD_TO_TAB_MAP[fieldPath];
  }
  
  // Check for nested fields (e.g., levelCheckboxStates.someId)
  const rootField = fieldPath.split('.')[0];
  return FIELD_TO_TAB_MAP[rootField];
};

/**
 * Get all field names for a specific tab
 * Useful for filtering or validation
 * 
 * @param tab - The tab name
 * @returns Array of field names belonging to the tab
 */
export const getFieldsForTab = (tab: ChartTabName): string[] => {
  return Object.entries(FIELD_TO_TAB_MAP)
    .filter(([, tabName]) => tabName === tab)
    .map(([fieldName]) => fieldName);
};