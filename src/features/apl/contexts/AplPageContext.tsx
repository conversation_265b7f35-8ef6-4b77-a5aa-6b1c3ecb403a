/**
 * AplPageContext
 * 
 * React Context that orchestrates the entire APL (Authorized Patient List) page state.
 * Combines multiple custom hooks to provide unified state management for form data,
 * facility selection, and search functionality. Handles complex interactions like
 * auto-fetching data when facilities change and syncing with app-level context.
 * 
 * Key concepts for React/TS newcomers:
 * - React Context: Provides shared state across component tree without prop drilling
 * - Custom hook composition: Combines multiple focused hooks into unified interface
 * - Provider pattern: Wraps components to provide context value
 * - useEffect coordination: Manages side effects and state synchronization
 * - ESLint rule management: Disables specific rules where necessary for complex logic
 * - TypeScript interfaces: Defines comprehensive type contracts for context value
 */
/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useContext, useEffect, useRef, useMemo, useCallback } from 'react';
import type { AplRecord } from '../services/aplRecordsService';
import { useFacilityData } from '../hooks/useFacilityData';
import type { FacilityListItem } from '../services/facilityService';
import { useAplSearch, type AplSearchFilters } from '../hooks/useAplSearch';
import { useFormState } from '../hooks/useFormState';
import { useFacilityActions } from '../../../shared/hooks/useFacilityInfoZustand';

interface AplPageContextType {
  // Form state
  visitId: string;
  mrn: string;
  fromDate: Date | null;
  toDate: Date | null;
  openWithoutDatalink: boolean;
  setVisitId: (visitId: string) => void;
  setMrn: (mrn: string) => void;
  setFromDate: (date: Date | null) => void;
  setToDate: (date: Date | null) => void;
  setOpenWithoutDatalink: (value: boolean) => void;
  updateDateWithParams: (newFromDate: Date | null, newToDate: Date | null) => void;
  
  // Facility data
  selectedFacilityId: string;
  setSelectedFacilityId: (facilityId: string) => void;
  facilities: FacilityListItem[];
  selectedFacilityData: FacilityListItem | null; // Full data for selected facility
  isLoadingFacilities: boolean;
  isFetchingFacilities: boolean;
  isErrorFacilities: boolean;
  facilityError: Error | { message?: string } | null;
  
  // APL search data
  aplRecords: AplRecord[];
  totalRecords: number;
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  loadAplRecords: () => Promise<void>;
  refreshAplRecords: () => Promise<void>;
  handleFilterChange: () => void;
}

const AplPageContext = createContext<AplPageContextType | undefined>(undefined);

export const useAplPageContext = () => {
  const context = useContext(AplPageContext);
  if (context === undefined) {
    throw new Error('useAplPageContext must be used within an AplPageProvider');
  }
  return context;
};

export const AplPageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use our focused hooks
  const facilityData = useFacilityData();
  const formState = useFormState();
  const aplSearch = useAplSearch();
  
  // Get app-level facility context for syncing
  const { setSelectedFacilityFull } = useFacilityActions();

  // Track facility changes to preserve original auto-fetch behavior (only when facility changes)
  const lastFetchedFacilityRef = useRef<string | null>(null);
  
  // Sync facility selection with app context whenever it changes
  useEffect(() => {
    // Sync the full facility data to app context
    if (facilityData.selectedFacilityListItem) {
      setSelectedFacilityFull(facilityData.selectedFacilityListItem);
    }
  }, [facilityData.selectedFacilityListItem, setSelectedFacilityFull]);
  
  // Intentionally NOT including formState.fromDate/toDate/visitId/mrn to avoid triggering on form changes
  /* eslint-disable react-hooks/exhaustive-deps */
  useEffect(() => {
    // Only auto-fetch when facility changes AND we're on APL route (preserving original behavior)
    const location = window.location;
    const isOnAplRoute = location.pathname.startsWith('/apl');
    
    // Don't auto-fetch if:
    // - Not on APL route
    // - No facility selected
    // - Facilities are still loading
    // - There's a facilities error
    if (!isOnAplRoute || 
        !facilityData.selectedFacilityId || 
        facilityData.isLoadingFacilities || 
        facilityData.isErrorFacilities) {
      return;
    }
    
    if (lastFetchedFacilityRef.current !== facilityData.selectedFacilityId) {
      // Auto-fetch when facility changes (original behavior)
      // Invalid dates will be caught and shown as error messages by useAplSearch
      const filters: AplSearchFilters = {
        facilityId: facilityData.selectedFacilityId,
        fromDate: formState.fromDate,
        toDate: formState.toDate,
        visitId: formState.visitId || undefined,
        mrn: formState.mrn || undefined,
      };
      
      aplSearch.loadAplRecords(filters);
      lastFetchedFacilityRef.current = facilityData.selectedFacilityId;
    }
  }, [
    facilityData.selectedFacilityId, 
    facilityData.isLoadingFacilities,
    facilityData.isErrorFacilities,
    aplSearch
  ]);
  /* eslint-enable react-hooks/exhaustive-deps */

  // Create wrapped loadAplRecords that uses current form state - memoized to prevent unnecessary re-renders
  const loadAplRecords = useCallback(async () => {
    // Invalid dates will be caught and shown as error messages by useAplSearch
    const filters: AplSearchFilters = {
      facilityId: facilityData.selectedFacilityId,
      fromDate: formState.fromDate,
      toDate: formState.toDate,
      visitId: formState.visitId || undefined,
      mrn: formState.mrn || undefined,
    };
    
    await aplSearch.loadAplRecords(filters);
  }, [
    facilityData.selectedFacilityId,
    formState.fromDate,
    formState.toDate,
    formState.visitId,
    formState.mrn,
    aplSearch
  ]);

  // Memoize the context value to prevent unnecessary re-renders of all consumers
  // Only memoize based on data values, not functions to avoid instability
  /* eslint-disable react-hooks/exhaustive-deps */
  const value: AplPageContextType = useMemo(() => ({
    // Form state
    visitId: formState.visitId,
    mrn: formState.mrn,
    fromDate: formState.fromDate,
    toDate: formState.toDate,
    openWithoutDatalink: formState.openWithoutDatalink,
    setVisitId: formState.setVisitId,
    setMrn: formState.setMrn,
    setFromDate: formState.setFromDate,
    setToDate: formState.setToDate,
    setOpenWithoutDatalink: formState.setOpenWithoutDatalink,
    updateDateWithParams: formState.updateDateWithParams,
    
    // Facility data
    selectedFacilityId: facilityData.selectedFacilityId,
    setSelectedFacilityId: facilityData.setSelectedFacilityId,
    facilities: facilityData.facilities,
    selectedFacilityData: facilityData.selectedFacilityListItem,
    isLoadingFacilities: facilityData.isLoadingFacilities,
    isFetchingFacilities: facilityData.isFetchingFacilities,
    isErrorFacilities: facilityData.isErrorFacilities,
    facilityError: facilityData.facilityError,
    
    // APL search data
    aplRecords: aplSearch.aplRecords,
    totalRecords: aplSearch.totalRecords,
    isLoading: aplSearch.isLoading,
    isFetching: aplSearch.isFetching,
    error: aplSearch.error,
    loadAplRecords,
    refreshAplRecords: aplSearch.refreshAplRecords,
    handleFilterChange: aplSearch.handleFilterChange,
  }), [
    // Only include stable references and actual data, not loading states
    // Form state dependencies (stable functions)
    formState.setVisitId,
    formState.setMrn,
    formState.setFromDate,
    formState.setToDate,
    formState.setOpenWithoutDatalink,
    formState.updateDateWithParams,
    
    // Facility data dependencies (stable functions and actual data)
    facilityData.setSelectedFacilityId,
    facilityData.facilities,
    facilityData.selectedFacilityId,
    
    // APL search data dependencies (stable functions)
    aplSearch.refreshAplRecords,
    aplSearch.handleFilterChange,
    loadAplRecords,
    
    // Reactive data - these will update automatically when hooks re-run
    // Removed: aplSearch.aplRecords, aplSearch.totalRecords, aplSearch.isLoading, aplSearch.isFetching, aplSearch.error
    // Removed: facilityData.isLoadingFacilities, facilityData.isFetchingFacilities, facilityData.isErrorFacilities, facilityData.facilityError
    // Removed: formState.visitId, formState.mrn, formState.fromDate, formState.toDate, formState.openWithoutDatalink
  ]);
  /* eslint-enable react-hooks/exhaustive-deps */

  return (
    <AplPageContext.Provider value={value}>
      {children}
    </AplPageContext.Provider>
  );
};
